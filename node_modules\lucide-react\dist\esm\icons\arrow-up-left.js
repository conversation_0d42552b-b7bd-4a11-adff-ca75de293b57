/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M7 17V7h10", key: "11bw93" }],
  ["path", { d: "M17 17 7 7", key: "2786uv" }]
];
const ArrowUpLeft = createLucideIcon("arrow-up-left", __iconNode);

export { __iconNode, ArrowUpLeft as default };
//# sourceMappingURL=arrow-up-left.js.map
