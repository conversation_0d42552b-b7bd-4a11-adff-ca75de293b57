/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "14", height: "6", x: "5", y: "16", rx: "2", key: "1i8z2d" }],
  ["rect", { width: "10", height: "6", x: "7", y: "6", rx: "2", key: "13squh" }],
  ["path", { d: "M2 2h20", key: "1ennik" }]
];
const AlignVerticalJustifyStart = createLucideIcon("align-vertical-justify-start", __iconNode);

export { __iconNode, AlignVerticalJustifyStart as default };
//# sourceMappingURL=align-vertical-justify-start.js.map
