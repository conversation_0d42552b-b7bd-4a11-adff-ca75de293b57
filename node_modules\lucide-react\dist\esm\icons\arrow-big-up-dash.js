/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M9 19h6", key: "456am0" }],
  ["path", { d: "M9 15v-3H5l7-7 7 7h-4v3H9z", key: "1r2uve" }]
];
const ArrowBigUpDash = createLucideIcon("arrow-big-up-dash", __iconNode);

export { __iconNode, ArrowBigUpDash as default };
//# sourceMappingURL=arrow-big-up-dash.js.map
